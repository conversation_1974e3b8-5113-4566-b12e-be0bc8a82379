// https://github.com/wojtyniak/mcp-mcp
// "Once configured, you can ask <PERSON> to discover MCP servers using natural language:

// "Find me an MCP server for weather data"
// "I need a server for checking domain availability"
// "Search for MCP servers related to stock market data"
// "What MCP servers are available for web scraping?""
{
  "mcpServers": {
    "mcp-mcp": {
      "command": "uvx",
      "args": ["mcp-mcp"]
    }
  }
}