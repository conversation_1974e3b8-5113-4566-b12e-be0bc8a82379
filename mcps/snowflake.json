"mcpServers": {
  "snowflake_pip": {
    "command": "uvx",
    "args": [
      "--python=3.12",  // Optional: specify Python version <=3.12
      "mcp_snowflake_server",
      "--account", "your_account",
      "--warehouse", "your_warehouse",
      "--user", "your_user",
      "--password", "your_password",
      "--role", "your_role",
      "--database", "your_database",
      "--schema", "your_schema"
      // Optionally: "--allow_write"
      // Optionally: "--log_dir", "/absolute/path/to/logs"
      // Optionally: "--log_level", "DEBUG"/"INFO"/"WARNING"/"ERROR"/"CRITICAL"
      // Optionally: "--exclude_tools", "{tool_name}", ["{other_tool_name}"]
    ]
  }
}