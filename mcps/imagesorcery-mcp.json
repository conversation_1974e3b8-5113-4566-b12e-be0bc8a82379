{
    "mcpServers": {
        "imagesorcery-mcp": {
            "command": "imagesorcery-mcp", // Or /full/path/to/venv/bin/imagesorcery-mcp if installed in a venv
            "transportType": "stdio",
            "autoApprove": [
                "blur",
                "change_color",
                "crop",
                "detect",
                "draw_arrows",
                "draw_circles",
                "draw_lines",
                "draw_rectangles",
                "draw_texts",
                "fill",
                "find",
                "get_metainfo",
                "ocr",
                "overlay",
                "resize",
                "rotate"
            ],
            "timeout": 100
        }
    }
}