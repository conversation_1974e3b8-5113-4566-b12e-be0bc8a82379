Ngrok gives you a unique TCP address like 
0.tcp.ngrok.io:12345 
that maps only to your Mac and port during that session.

This is necessary when we use mobile data like Safaricom and we need port forwarding to our machine
because Safaricom has CGNAT (Carrier-Grade NAT) - the firewall

Even if your Mac is listening on eg port 5900 (or any port), 
nobody on the public internet can reach your machine.

# installation
brew install ngrok/ngrok/ngrok
