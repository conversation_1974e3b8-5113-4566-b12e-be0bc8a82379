#!/usr/bin/env python3
"""
Gemini Screenshot Analyzer for Remote macOS Control

This tool analyzes screenshots from remote macOS control using Google's Gemini API
to identify active applications, UI states, and provide automation guidance.
"""

import os
import base64
import json
import requests
from typing import Dict, Any, Optional
from pathlib import Path
import tempfile
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class GeminiScreenshotAnalyzer:
    def __init__(self):
        self.api_key = os.getenv('api_key')
        if not self.api_key:
            raise ValueError("Gemini API key not found in environment variables")
        
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
        
    def encode_image_to_base64(self, image_data: bytes) -> str:
        """Convert image bytes to base64 string for Gemini API"""
        return base64.b64encode(image_data).decode('utf-8')
    
    def get_dynamic_instructions(self, image_data: bytes, intended_goal: str = "") -> Dict[str, Any]:
        """
        Get dynamic, step-by-step instructions by asking multiple targeted questions

        Args:
            image_data: Raw image bytes from screenshot
            intended_goal: What we're trying to achieve (e.g., "open monkeytype.com", "start typing test")

        Returns:
            Comprehensive analysis with specific instructions
        """

        goal_context = f"My goal is: {intended_goal}" if intended_goal else "I need automation guidance"

        prompt = f"""Analyze this macOS screenshot and provide detailed automation guidance. {goal_context}

Please answer these specific questions:

🎯 **CURRENT STATE ANALYSIS:**
1. Which application is currently active/focused on the screen?
2. Where is the cursor currently positioned or focused?
3. What UI elements are visible and interactive?
4. Are there any loading indicators, dialogs, or blocking elements?

🔍 **READINESS ASSESSMENT:**
5. Is the current application ready for input (keyboard/mouse)?
6. Are there any obstacles that need to be handled first?
7. What is the current state of the active window (loading, ready, error, etc.)?

⚡ **NEXT ACTION INSTRUCTIONS:**
8. What specific action should be performed next to progress toward the goal?
9. What exact keyboard shortcut, mouse click, or text input is needed?
10. Where exactly should I click (describe the location/element)?
11. What text should I type (if any)?

⏱️ **TIMING & SEQUENCE:**
12. Should I wait for anything before taking the next action?
13. How long should I wait (if any waiting is needed)?
14. Are there multiple steps needed in sequence?

🚨 **WARNINGS & CONSIDERATIONS:**
15. Are there any potential issues or things to be careful about?
16. What could go wrong with the next action?
17. How can I verify the action was successful?

Please be very specific and actionable in your responses. Use clear, step-by-step instructions."""

        return self._make_api_request(image_data, prompt, "dynamic_instructions")

    def analyze_screenshot(self, image_data: bytes, analysis_type: str = "general") -> Dict[str, Any]:
        """
        Analyze screenshot using Gemini API
        
        Args:
            image_data: Raw image bytes from screenshot
            analysis_type: Type of analysis to perform
                - "general": General screen analysis
                - "app_identification": Identify active application
                - "ui_state": Analyze UI state and readiness
                - "automation_guidance": Provide next action suggestions
        
        Returns:
            Dictionary with analysis results
        """
        
        # Encode image
        image_base64 = self.encode_image_to_base64(image_data)
        
        # Create prompt based on analysis type
        prompts = {
            "general": """Analyze this macOS screenshot and provide:
1. Which application is currently active/focused
2. Current UI state and any visible elements
3. Whether the app appears ready for input
4. Any loading indicators or modal dialogs
5. Suggested next actions for automation""",
            
            "app_identification": """Look at this macOS screenshot and identify:
1. Which application is currently active (has focus)
2. Is the application window in the foreground?
3. Are there any other visible applications?
4. What is the current state of the active application?""",
            
            "ui_state": """Analyze the UI state in this macOS screenshot:
1. Is the application ready for input?
2. Are there any loading indicators?
3. Are there any modal dialogs or popups?
4. What UI elements are currently visible and interactive?
5. Is there a text cursor or input field focused?""",
            
            "automation_guidance": """Based on this macOS screenshot, provide automation guidance:
1. What is the current application and its state?
2. What would be the next logical action for browser/app automation?
3. Are there any obstacles (loading, dialogs, etc.) that need to be handled first?
4. What keyboard shortcuts or mouse actions would be most appropriate?
5. Any timing considerations for the next action?"""
        }
        
        prompt = prompts.get(analysis_type, prompts["general"])
        
        return self._make_api_request(image_data, prompt, analysis_type)

    def _make_api_request(self, image_data: bytes, prompt: str, analysis_type: str) -> Dict[str, Any]:
        """Make API request to Gemini with image and prompt"""

        # Encode image
        image_base64 = self.encode_image_to_base64(image_data)

        # Prepare API request
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        },
                        {
                            "inline_data": {
                                "mime_type": "image/png",
                                "data": image_base64
                            }
                        }
                    ]
                }
            ]
        }

        headers = {
            "Content-Type": "application/json"
        }

        # Make API request
        try:
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers=headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()

            result = response.json()

            # Extract text from response
            if 'candidates' in result and len(result['candidates']) > 0:
                content = result['candidates'][0]['content']['parts'][0]['text']
                return {
                    "success": True,
                    "analysis": content,
                    "analysis_type": analysis_type,
                    "raw_response": result
                }
            else:
                return {
                    "success": False,
                    "error": "No analysis content in response",
                    "raw_response": result
                }

        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": f"API request failed: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Analysis failed: {str(e)}"
            }
    
    def quick_app_check(self, image_data: bytes) -> str:
        """Quick check to identify the active application"""
        result = self.analyze_screenshot(image_data, "app_identification")
        if result["success"]:
            return result["analysis"]
        else:
            return f"Analysis failed: {result.get('error', 'Unknown error')}"
    
    def automation_advice(self, image_data: bytes) -> str:
        """Get automation guidance for the current screen state"""
        result = self.analyze_screenshot(image_data, "automation_guidance")
        if result["success"]:
            return result["analysis"]
        else:
            return f"Analysis failed: {result.get('error', 'Unknown error')}"

    def get_step_by_step_instructions(self, image_data: bytes, goal: str) -> Dict[str, Any]:
        """
        Get comprehensive step-by-step instructions for achieving a specific goal

        Args:
            image_data: Screenshot data
            goal: What we want to achieve

        Returns:
            Parsed instructions with specific actions
        """
        result = self.get_dynamic_instructions(image_data, goal)

        if not result["success"]:
            return result

        # Parse the response to extract actionable information
        analysis_text = result["analysis"]

        parsed_instructions = {
            "success": True,
            "goal": goal,
            "current_app": self._extract_info(analysis_text, ["application", "app", "focused"]),
            "cursor_location": self._extract_info(analysis_text, ["cursor", "focused", "position"]),
            "readiness_status": self._extract_info(analysis_text, ["ready", "loading", "wait"]),
            "next_action": self._extract_info(analysis_text, ["next action", "should", "perform"]),
            "specific_command": self._extract_info(analysis_text, ["keyboard", "click", "type", "shortcut"]),
            "timing_advice": self._extract_info(analysis_text, ["wait", "timing", "delay"]),
            "warnings": self._extract_info(analysis_text, ["warning", "careful", "issue"]),
            "verification": self._extract_info(analysis_text, ["verify", "check", "confirm"]),
            "full_analysis": analysis_text
        }

        return parsed_instructions

    def _extract_info(self, text: str, keywords: list) -> str:
        """Extract relevant information from analysis text based on keywords"""
        lines = text.split('\n')
        relevant_lines = []

        for line in lines:
            if any(keyword.lower() in line.lower() for keyword in keywords):
                relevant_lines.append(line.strip())

        return '\n'.join(relevant_lines) if relevant_lines else "Not specified"

def main():
    """CLI interface for testing the analyzer"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python gemini_screenshot_analyzer.py <image_path> [analysis_type]")
        print("Analysis types: general, app_identification, ui_state, automation_guidance")
        return
    
    image_path = sys.argv[1]
    analysis_type = sys.argv[2] if len(sys.argv) > 2 else "general"
    
    try:
        analyzer = GeminiScreenshotAnalyzer()
        
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        result = analyzer.analyze_screenshot(image_data, analysis_type)
        
        if result["success"]:
            print("=== GEMINI ANALYSIS ===")
            print(result["analysis"])
        else:
            print(f"Error: {result['error']}")
            
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
