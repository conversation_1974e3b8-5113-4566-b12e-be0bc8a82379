#!/usr/bin/env python3
"""
Gemini Screenshot Analyzer for Remote macOS Control

This tool analyzes screenshots from remote macOS control using Google's Gemini API
to identify active applications, UI states, and provide automation guidance.
"""

import os
import base64
import json
import requests
from typing import Dict, Any, Optional
from pathlib import Path
import tempfile
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class GeminiScreenshotAnalyzer:
    def __init__(self):
        self.api_key = os.getenv('api_key')
        if not self.api_key:
            raise ValueError("Gemini API key not found in environment variables")
        
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
        
    def encode_image_to_base64(self, image_data: bytes) -> str:
        """Convert image bytes to base64 string for Gemini API"""
        return base64.b64encode(image_data).decode('utf-8')
    
    def analyze_screenshot(self, image_data: bytes, analysis_type: str = "general") -> Dict[str, Any]:
        """
        Analyze screenshot using Gemini API
        
        Args:
            image_data: Raw image bytes from screenshot
            analysis_type: Type of analysis to perform
                - "general": General screen analysis
                - "app_identification": Identify active application
                - "ui_state": Analyze UI state and readiness
                - "automation_guidance": Provide next action suggestions
        
        Returns:
            Dictionary with analysis results
        """
        
        # Encode image
        image_base64 = self.encode_image_to_base64(image_data)
        
        # Create prompt based on analysis type
        prompts = {
            "general": """Analyze this macOS screenshot and provide:
1. Which application is currently active/focused
2. Current UI state and any visible elements
3. Whether the app appears ready for input
4. Any loading indicators or modal dialogs
5. Suggested next actions for automation""",
            
            "app_identification": """Look at this macOS screenshot and identify:
1. Which application is currently active (has focus)
2. Is the application window in the foreground?
3. Are there any other visible applications?
4. What is the current state of the active application?""",
            
            "ui_state": """Analyze the UI state in this macOS screenshot:
1. Is the application ready for input?
2. Are there any loading indicators?
3. Are there any modal dialogs or popups?
4. What UI elements are currently visible and interactive?
5. Is there a text cursor or input field focused?""",
            
            "automation_guidance": """Based on this macOS screenshot, provide automation guidance:
1. What is the current application and its state?
2. What would be the next logical action for browser/app automation?
3. Are there any obstacles (loading, dialogs, etc.) that need to be handled first?
4. What keyboard shortcuts or mouse actions would be most appropriate?
5. Any timing considerations for the next action?"""
        }
        
        prompt = prompts.get(analysis_type, prompts["general"])
        
        # Prepare API request
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        },
                        {
                            "inline_data": {
                                "mime_type": "image/png",
                                "data": image_base64
                            }
                        }
                    ]
                }
            ]
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        # Make API request
        try:
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers=headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            
            # Extract text from response
            if 'candidates' in result and len(result['candidates']) > 0:
                content = result['candidates'][0]['content']['parts'][0]['text']
                return {
                    "success": True,
                    "analysis": content,
                    "analysis_type": analysis_type,
                    "raw_response": result
                }
            else:
                return {
                    "success": False,
                    "error": "No analysis content in response",
                    "raw_response": result
                }
                
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": f"API request failed: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Analysis failed: {str(e)}"
            }
    
    def quick_app_check(self, image_data: bytes) -> str:
        """Quick check to identify the active application"""
        result = self.analyze_screenshot(image_data, "app_identification")
        if result["success"]:
            return result["analysis"]
        else:
            return f"Analysis failed: {result.get('error', 'Unknown error')}"
    
    def automation_advice(self, image_data: bytes) -> str:
        """Get automation guidance for the current screen state"""
        result = self.analyze_screenshot(image_data, "automation_guidance")
        if result["success"]:
            return result["analysis"]
        else:
            return f"Analysis failed: {result.get('error', 'Unknown error')}"

def main():
    """CLI interface for testing the analyzer"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python gemini_screenshot_analyzer.py <image_path> [analysis_type]")
        print("Analysis types: general, app_identification, ui_state, automation_guidance")
        return
    
    image_path = sys.argv[1]
    analysis_type = sys.argv[2] if len(sys.argv) > 2 else "general"
    
    try:
        analyzer = GeminiScreenshotAnalyzer()
        
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        result = analyzer.analyze_screenshot(image_data, analysis_type)
        
        if result["success"]:
            print("=== GEMINI ANALYSIS ===")
            print(result["analysis"])
        else:
            print(f"Error: {result['error']}")
            
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
