#!/usr/bin/env python3
"""
Multi-AI Screenshot Analyzer with Fallback Support

This tool provides screenshot analysis using multiple AI services:
1. Primary: Google Gemini
2. Backup: Perplexity (when <PERSON> fails)

Ensures reliable automation guidance even if one service is unavailable.
"""

import os
import base64
import json
import requests
from typing import Dict, Any, Optional, List
from pathlib import Path
import tempfile
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class MultiAIScreenshotAnalyzer:
    def __init__(self):
        # Gemini configuration
        self.gemini_api_key = os.getenv('api_key')
        self.gemini_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
        
        # Perplexity configuration (assuming it's in environment)
        self.perplexity_api_key = os.getenv('PERPLEXITY_API_KEY')
        self.perplexity_url = "https://api.perplexity.ai/chat/completions"
        
        # Track which services are available
        self.gemini_available = bool(self.gemini_api_key)
        self.perplexity_available = bool(self.perplexity_api_key)
        
        if not (self.gemini_available or self.perplexity_available):
            raise ValueError("No AI service API keys found. Need either Gemini or Perplexity API key.")
    
    def analyze_screenshot_with_fallback(self, image_data: bytes, goal: str = "", analysis_type: str = "automation") -> Dict[str, Any]:
        """
        Analyze screenshot with automatic fallback between AI services
        
        Args:
            image_data: Raw image bytes
            goal: What we're trying to achieve
            analysis_type: Type of analysis needed
            
        Returns:
            Analysis results with service used
        """
        
        # Try Gemini first (primary)
        if self.gemini_available:
            try:
                result = self._analyze_with_gemini(image_data, goal, analysis_type)
                if result["success"]:
                    result["service_used"] = "gemini"
                    result["fallback_used"] = False
                    return result
                else:
                    print(f"⚠️ Gemini failed: {result.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"⚠️ Gemini exception: {str(e)}")
        
        # Fallback to Perplexity
        if self.perplexity_available:
            try:
                result = self._analyze_with_perplexity(image_data, goal, analysis_type)
                if result["success"]:
                    result["service_used"] = "perplexity"
                    result["fallback_used"] = True
                    return result
                else:
                    print(f"⚠️ Perplexity failed: {result.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"⚠️ Perplexity exception: {str(e)}")
        
        # Both services failed
        return {
            "success": False,
            "error": "All AI services failed",
            "service_used": "none",
            "fallback_used": True
        }
    
    def _analyze_with_gemini(self, image_data: bytes, goal: str, analysis_type: str) -> Dict[str, Any]:
        """Analyze using Google Gemini"""
        
        prompt = self._create_analysis_prompt(goal, analysis_type)
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        payload = {
            "contents": [
                {
                    "parts": [
                        {"text": prompt},
                        {
                            "inline_data": {
                                "mime_type": "image/png",
                                "data": image_base64
                            }
                        }
                    ]
                }
            ]
        }
        
        headers = {"Content-Type": "application/json"}
        
        response = requests.post(
            f"{self.gemini_url}?key={self.gemini_api_key}",
            headers=headers,
            json=payload,
            timeout=30
        )
        response.raise_for_status()
        
        result = response.json()
        
        if 'candidates' in result and len(result['candidates']) > 0:
            content = result['candidates'][0]['content']['parts'][0]['text']
            return {
                "success": True,
                "analysis": content,
                "analysis_type": analysis_type
            }
        else:
            return {
                "success": False,
                "error": "No analysis content in Gemini response"
            }
    
    def _analyze_with_perplexity(self, image_data: bytes, goal: str, analysis_type: str) -> Dict[str, Any]:
        """Analyze using Perplexity"""
        
        prompt = self._create_analysis_prompt(goal, analysis_type)
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        payload = {
            "model": "sonar-pro",  # Supports image input
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
        }
        
        headers = {
            "Authorization": f"Bearer {self.perplexity_api_key}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            self.perplexity_url,
            headers=headers,
            json=payload,
            timeout=30
        )
        response.raise_for_status()
        
        result = response.json()
        
        if 'choices' in result and len(result['choices']) > 0:
            content = result['choices'][0]['message']['content']
            return {
                "success": True,
                "analysis": content,
                "analysis_type": analysis_type
            }
        else:
            return {
                "success": False,
                "error": "No analysis content in Perplexity response"
            }
    
    def _create_analysis_prompt(self, goal: str, analysis_type: str) -> str:
        """Create analysis prompt based on type and goal"""
        
        goal_context = f"My goal is: {goal}" if goal else "I need automation guidance"
        
        if analysis_type == "dynamic":
            return f"""Analyze this macOS screenshot and provide detailed automation guidance. {goal_context}

Please answer these specific questions:

🎯 **CURRENT STATE ANALYSIS:**
1. Which application is currently active/focused on the screen?
2. Where is the cursor currently positioned or focused?
3. What UI elements are visible and interactive?
4. Are there any loading indicators, dialogs, or blocking elements?

🔍 **READINESS ASSESSMENT:**
5. Is the current application ready for input (keyboard/mouse)?
6. Are there any obstacles that need to be handled first?
7. What is the current state of the active window (loading, ready, error, etc.)?

⚡ **NEXT ACTION INSTRUCTIONS:**
8. What specific action should be performed next to progress toward the goal?
9. What exact keyboard shortcut, mouse click, or text input is needed?
10. Where exactly should I click (describe the location/element)?
11. What text should I type (if any)?

⏱️ **TIMING & SEQUENCE:**
12. Should I wait for anything before taking the next action?
13. How long should I wait (if any waiting is needed)?
14. Are there multiple steps needed in sequence?

🚨 **WARNINGS & CONSIDERATIONS:**
15. Are there any potential issues or things to be careful about?
16. What could go wrong with the next action?
17. How can I verify the action was successful?

Please be very specific and actionable in your responses."""
        
        elif analysis_type == "app_check":
            return f"Look at this macOS screenshot and identify: 1) Which application is currently active/focused, 2) Is the application ready for input, 3) What is the current state. {goal_context}"
        
        elif analysis_type == "quick":
            return f"Based on this macOS screenshot, what is the next specific action I should take? {goal_context}. Be very specific about keyboard shortcuts, mouse clicks, or text to type."
        
        else:  # general
            return f"Analyze this macOS screenshot for automation purposes. Identify the active app, current state, and suggest the next action. {goal_context}"
    
    def get_service_status(self) -> Dict[str, bool]:
        """Get status of available AI services"""
        return {
            "gemini_available": self.gemini_available,
            "perplexity_available": self.perplexity_available,
            "total_services": sum([self.gemini_available, self.perplexity_available])
        }

def analyze_with_fallback(image_data: bytes, goal: str = "", analysis_type: str = "dynamic") -> str:
    """
    Simple function to analyze screenshot with automatic AI service fallback
    """
    try:
        analyzer = MultiAIScreenshotAnalyzer()
        result = analyzer.analyze_screenshot_with_fallback(image_data, goal, analysis_type)
        
        if result["success"]:
            service_info = f"🤖 **{result['service_used'].upper()}**"
            if result.get("fallback_used"):
                service_info += " (fallback)"
            
            return f"{service_info}:\n\n{result['analysis']}"
        else:
            return f"❌ Analysis failed: {result['error']}"
    except Exception as e:
        return f"❌ Error during analysis: {str(e)}"

def main():
    """CLI interface"""
    import sys
    
    if len(sys.argv) < 2:
        print("Multi-AI Screenshot Analyzer with Fallback")
        print("Usage: python multi_ai_screenshot_analyzer.py <image_path> [goal] [analysis_type]")
        print("Analysis types: dynamic, app_check, quick, general")
        return
    
    image_path = sys.argv[1]
    goal = sys.argv[2] if len(sys.argv) > 2 else ""
    analysis_type = sys.argv[3] if len(sys.argv) > 3 else "dynamic"
    
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        result = analyze_with_fallback(image_data, goal, analysis_type)
        print(result)
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
