#!/usr/bin/env python3
"""
Remote macOS Control with Gemini Analysis Integration

This module provides helper functions to integrate Gemini screenshot analysis
with remote macOS control tasks for better automation decision-making.
"""

import asyncio
import json
from typing import Dict, Any, Optional
from gemini_screenshot_analyzer import GeminiScreenshotAnalyzer

class RemoteMacOSWithGemini:
    def __init__(self):
        self.analyzer = GeminiScreenshotAnalyzer()
    
    def analyze_current_screen(self, screenshot_data: bytes, purpose: str = "automation") -> Dict[str, Any]:
        """
        Analyze current screen state for automation purposes
        
        Args:
            screenshot_data: Raw screenshot bytes from remote_macos_get_screen
            purpose: Purpose of analysis
                - "automation": General automation guidance
                - "app_check": Check which app is active
                - "readiness": Check if app is ready for input
                - "next_action": Get specific next action advice
        
        Returns:
            Analysis results with recommendations
        """
        
        analysis_map = {
            "automation": "automation_guidance",
            "app_check": "app_identification", 
            "readiness": "ui_state",
            "next_action": "automation_guidance"
        }
        
        analysis_type = analysis_map.get(purpose, "general")
        result = self.analyzer.analyze_screenshot(screenshot_data, analysis_type)
        
        if result["success"]:
            return {
                "success": True,
                "analysis": result["analysis"],
                "purpose": purpose,
                "recommendations": self._extract_recommendations(result["analysis"], purpose)
            }
        else:
            return {
                "success": False,
                "error": result["error"],
                "purpose": purpose
            }
    
    def _extract_recommendations(self, analysis_text: str, purpose: str) -> Dict[str, Any]:
        """Extract actionable recommendations from analysis text"""
        
        recommendations = {
            "active_app": None,
            "ready_for_input": None,
            "suggested_actions": [],
            "warnings": [],
            "timing_advice": None
        }
        
        # Simple text parsing to extract key information
        lines = analysis_text.lower().split('\n')
        
        for line in lines:
            # Identify active application
            if any(app in line for app in ['chrome', 'safari', 'firefox', 'browser']):
                if 'active' in line or 'focused' in line or 'foreground' in line:
                    recommendations["active_app"] = "browser"
            elif any(app in line for app in ['vscode', 'code', 'editor']):
                if 'active' in line or 'focused' in line:
                    recommendations["active_app"] = "vscode"
            elif any(app in line for app in ['terminal', 'iterm']):
                if 'active' in line or 'focused' in line:
                    recommendations["active_app"] = "terminal"
            
            # Check readiness
            if any(word in line for word in ['ready', 'input', 'cursor', 'focused']):
                recommendations["ready_for_input"] = True
            elif any(word in line for word in ['loading', 'wait', 'not ready']):
                recommendations["ready_for_input"] = False
            
            # Extract warnings
            if any(word in line for word in ['warning', 'caution', 'wait', 'loading']):
                recommendations["warnings"].append(line.strip())
            
            # Extract timing advice
            if any(word in line for word in ['timing', 'delay', 'wait', 'seconds']):
                recommendations["timing_advice"] = line.strip()
        
        return recommendations
    
    def get_automation_advice(self, screenshot_data: bytes, intended_action: str) -> str:
        """
        Get specific advice for an intended automation action
        
        Args:
            screenshot_data: Current screenshot
            intended_action: What you plan to do (e.g., "open new tab", "type text", "click button")
        
        Returns:
            Specific advice for the intended action
        """
        
        custom_prompt = f"""
        I want to perform this action: "{intended_action}"
        
        Based on the current macOS screenshot, please advise:
        1. Is the current application ready for this action?
        2. What should I do first to prepare for this action?
        3. Are there any obstacles or issues I should handle first?
        4. What is the best way to execute this action?
        5. Any timing or sequencing considerations?
        
        Please be specific and actionable in your advice.
        """
        
        # Use the general analysis with custom prompt
        result = self.analyzer.analyze_screenshot(screenshot_data, "general")
        
        if result["success"]:
            return result["analysis"]
        else:
            return f"Analysis failed: {result.get('error', 'Unknown error')}"

# Helper functions for easy integration with MCP tools
def analyze_screenshot_for_automation(screenshot_data: bytes, purpose: str = "automation") -> str:
    """
    Simple function to analyze screenshot and return text advice
    Can be easily called from MCP remote control workflows
    """
    try:
        helper = RemoteMacOSWithGemini()
        result = helper.analyze_current_screen(screenshot_data, purpose)
        
        if result["success"]:
            return f"=== GEMINI ANALYSIS ===\n{result['analysis']}"
        else:
            return f"Analysis failed: {result['error']}"
    except Exception as e:
        return f"Error during analysis: {str(e)}"

def quick_app_identification(screenshot_data: bytes) -> str:
    """Quick function to identify the active application"""
    return analyze_screenshot_for_automation(screenshot_data, "app_check")

def check_app_readiness(screenshot_data: bytes) -> str:
    """Quick function to check if the app is ready for input"""
    return analyze_screenshot_for_automation(screenshot_data, "readiness")

def get_next_action_advice(screenshot_data: bytes) -> str:
    """Quick function to get advice on next automation action"""
    return analyze_screenshot_for_automation(screenshot_data, "next_action")

# Example usage function
def demo_integration():
    """
    Example of how to integrate this with remote macOS control
    """
    print("Example integration with remote macOS control:")
    print("1. Take screenshot with remote_macos_get_screen")
    print("2. Analyze with Gemini: analyze_screenshot_for_automation(screenshot_data)")
    print("3. Follow the advice for next actions")
    print("4. Execute remote macOS commands based on analysis")

if __name__ == "__main__":
    demo_integration()
