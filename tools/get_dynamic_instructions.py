#!/usr/bin/env python3
"""
Dynamic Instruction Generator for Remote macOS Control

This tool asks Gemini multiple targeted questions to provide comprehensive,
step-by-step automation guidance based on the current screen state.
"""

import sys
import os
import base64
import json
from pathlib import Path

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_screenshot_analyzer import GeminiScreenshotAnalyzer

def get_dynamic_automation_instructions(image_data: bytes, goal: str = "") -> str:
    """
    Get comprehensive automation instructions by asking Gemini multiple questions
    
    Args:
        image_data: Screenshot bytes
        goal: What we're trying to achieve
        
    Returns:
        Formatted instruction text
    """
    try:
        analyzer = GeminiScreenshotAnalyzer()
        result = analyzer.get_step_by_step_instructions(image_data, goal)
        
        if result["success"]:
            # Format the instructions nicely
            output = f"""
🎯 **GOAL:** {result['goal']}

📱 **CURRENT STATE:**
• Active App: {result['current_app']}
• Cursor Location: {result['cursor_location']}
• Readiness: {result['readiness_status']}

⚡ **NEXT ACTION:**
{result['next_action']}

🔧 **SPECIFIC COMMAND:**
{result['specific_command']}

⏱️ **TIMING:**
{result['timing_advice']}

⚠️ **WARNINGS:**
{result['warnings']}

✅ **VERIFICATION:**
{result['verification']}

📋 **FULL ANALYSIS:**
{result['full_analysis']}
"""
            return output
        else:
            return f"❌ Analysis failed: {result.get('error', 'Unknown error')}"
            
    except Exception as e:
        return f"❌ Error during analysis: {str(e)}"

def get_quick_next_step(image_data: bytes, goal: str = "") -> str:
    """
    Get just the essential next step information
    
    Args:
        image_data: Screenshot bytes
        goal: What we're trying to achieve
        
    Returns:
        Quick instruction text
    """
    try:
        analyzer = GeminiScreenshotAnalyzer()
        result = analyzer.get_dynamic_instructions(image_data, goal)
        
        if result["success"]:
            # Extract just the key action items
            analysis = result["analysis"]
            
            # Look for the next action section
            lines = analysis.split('\n')
            next_action_lines = []
            in_next_action = False
            
            for line in lines:
                if any(phrase in line.lower() for phrase in ['next action', 'what should', 'perform next']):
                    in_next_action = True
                    next_action_lines.append(line)
                elif in_next_action and line.strip():
                    if line.startswith(('8.', '9.', '10.', '11.')):
                        next_action_lines.append(line)
                    elif any(phrase in line.lower() for phrase in ['timing', 'warning', 'wait']):
                        next_action_lines.append(line)
                        break
            
            if next_action_lines:
                return f"🚀 **QUICK INSTRUCTION:**\n" + '\n'.join(next_action_lines)
            else:
                return f"🤖 **GEMINI SAYS:**\n{analysis[:500]}..."
        else:
            return f"❌ Analysis failed: {result.get('error', 'Unknown error')}"
            
    except Exception as e:
        return f"❌ Error during analysis: {str(e)}"

def analyze_from_base64(image_base64: str, goal: str = "", mode: str = "full") -> str:
    """
    Analyze screen from base64 image data
    
    Args:
        image_base64: Base64 encoded image data
        goal: What we're trying to achieve
        mode: 'full' for complete analysis, 'quick' for just next steps
        
    Returns:
        Analysis text
    """
    try:
        # Decode base64 to bytes
        image_data = base64.b64decode(image_base64)
        
        if mode == "quick":
            return get_quick_next_step(image_data, goal)
        else:
            return get_dynamic_automation_instructions(image_data, goal)
            
    except Exception as e:
        return f"❌ Error decoding image: {str(e)}"

def analyze_from_file(image_path: str, goal: str = "", mode: str = "full") -> str:
    """
    Analyze screen from image file
    
    Args:
        image_path: Path to image file
        goal: What we're trying to achieve
        mode: 'full' for complete analysis, 'quick' for just next steps
        
    Returns:
        Analysis text
    """
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        if mode == "quick":
            return get_quick_next_step(image_data, goal)
        else:
            return get_dynamic_automation_instructions(image_data, goal)
            
    except Exception as e:
        return f"❌ Error reading image file: {str(e)}"

def main():
    """CLI interface for dynamic instructions"""
    if len(sys.argv) < 2:
        print("🤖 Dynamic Instruction Generator for Remote macOS Control")
        print("")
        print("Usage:")
        print("  python get_dynamic_instructions.py <image_path> [goal] [mode]")
        print("  python get_dynamic_instructions.py --base64 <base64_data> [goal] [mode]")
        print("")
        print("Arguments:")
        print("  goal  - What you're trying to achieve (e.g., 'open monkeytype.com')")
        print("  mode  - 'full' for complete analysis (default), 'quick' for just next steps")
        print("")
        print("Examples:")
        print("  python get_dynamic_instructions.py screenshot.png 'start typing test'")
        print("  python get_dynamic_instructions.py screenshot.png 'open new tab' quick")
        return
    
    if sys.argv[1] == "--base64":
        if len(sys.argv) < 3:
            print("❌ Error: Base64 data required")
            return
        
        image_base64 = sys.argv[2]
        goal = sys.argv[3] if len(sys.argv) > 3 else ""
        mode = sys.argv[4] if len(sys.argv) > 4 else "full"
        
        result = analyze_from_base64(image_base64, goal, mode)
        print(result)
        
    else:
        image_path = sys.argv[1]
        goal = sys.argv[2] if len(sys.argv) > 2 else ""
        mode = sys.argv[3] if len(sys.argv) > 3 else "full"
        
        result = analyze_from_file(image_path, goal, mode)
        print(result)

if __name__ == "__main__":
    main()
