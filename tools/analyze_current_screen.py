#!/usr/bin/env python3
"""
Quick Screen Analysis Tool

This script can be called during remote macOS automation to get
real-time analysis of the current screen state using Gemini.
"""

import sys
import os
import tempfile
import base64
from pathlib import Path

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_screenshot_analyzer import GeminiScreenshotAnalyzer

def analyze_screen_from_base64(image_base64: str, analysis_type: str = "automation") -> str:
    """
    Analyze screen from base64 image data
    
    Args:
        image_base64: Base64 encoded image data
        analysis_type: Type of analysis (automation, app_check, readiness, next_action)
    
    Returns:
        Analysis text from Gemini
    """
    try:
        # Decode base64 to bytes
        image_data = base64.b64decode(image_base64)
        
        # Initialize analyzer
        analyzer = GeminiScreenshotAnalyzer()
        
        # Map analysis types
        analysis_map = {
            "automation": "automation_guidance",
            "app_check": "app_identification", 
            "readiness": "ui_state",
            "next_action": "automation_guidance",
            "general": "general"
        }
        
        gemini_analysis_type = analysis_map.get(analysis_type, "automation_guidance")
        
        # Perform analysis
        result = analyzer.analyze_screenshot(image_data, gemini_analysis_type)
        
        if result["success"]:
            return f"🤖 GEMINI ANALYSIS ({analysis_type.upper()}):\n\n{result['analysis']}"
        else:
            return f"❌ Analysis failed: {result.get('error', 'Unknown error')}"
            
    except Exception as e:
        return f"❌ Error during analysis: {str(e)}"

def analyze_screen_from_file(image_path: str, analysis_type: str = "automation") -> str:
    """
    Analyze screen from image file
    
    Args:
        image_path: Path to image file
        analysis_type: Type of analysis
    
    Returns:
        Analysis text from Gemini
    """
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        return analyze_screen_from_base64(image_base64, analysis_type)
        
    except Exception as e:
        return f"❌ Error reading image file: {str(e)}"

def main():
    """CLI interface"""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python analyze_current_screen.py <image_path> [analysis_type]")
        print("  python analyze_current_screen.py --base64 <base64_data> [analysis_type]")
        print("")
        print("Analysis types:")
        print("  automation  - General automation guidance (default)")
        print("  app_check   - Identify active application")
        print("  readiness   - Check if app is ready for input")
        print("  next_action - Get specific next action advice")
        print("  general     - General screen analysis")
        return
    
    if sys.argv[1] == "--base64":
        if len(sys.argv) < 3:
            print("Error: Base64 data required")
            return
        
        image_base64 = sys.argv[2]
        analysis_type = sys.argv[3] if len(sys.argv) > 3 else "automation"
        
        result = analyze_screen_from_base64(image_base64, analysis_type)
        print(result)
        
    else:
        image_path = sys.argv[1]
        analysis_type = sys.argv[2] if len(sys.argv) > 2 else "automation"
        
        result = analyze_screen_from_file(image_path, analysis_type)
        print(result)

if __name__ == "__main__":
    main()
