{"cells": [{"cell_type": "markdown", "id": "58dbab4d", "metadata": {}, "source": ["# MCP Server (Both local and remote) Testing Arena"]}, {"cell_type": "code", "execution_count": 2, "id": "8978f5ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello World\n"]}], "source": ["print(\"Hello World\")"]}, {"cell_type": "code", "execution_count": null, "id": "b552694e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}