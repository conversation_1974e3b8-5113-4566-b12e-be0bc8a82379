[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "computer-control-mcp"
version = "0.2.7"
description = "MCP server that provides computer control capabilities, like mouse, keyboard, OCR, etc. using PyAutoGUI, RapidOCR, ONNXRuntime. Similar to 'computer-use' by Anthropic. With Zero External Dependencies."
readme = "README.md"
requires-python = ">=3.12"
license = {text = "MIT"}
authors = [
    {name = "AB498", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries",
    "Topic :: Utilities"
]
dependencies = [
    "pyautogui",
    "mcp[cli]",
    "pillow",
    "pygetwindow",
    "fuzzywuzzy",
    "rapidocr",
    "onnxruntime",
    "rapidocr_onnxruntime",
    "opencv-python"
]

[project.urls]
Homepage = "https://github.com/AB498/computer-control-mcp"
Issues = "https://github.com/AB498/computer-control-mcp/issues"
Documentation = "https://github.com/AB498/computer-control-mcp#readme"

[project.scripts]
computer-control-mcp = "computer_control_mcp.cli:main"
computer-control-mcp-server = "computer_control_mcp.server:main"

[tool.hatch.build.targets.wheel]
packages = ["src/computer_control_mcp"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"

[tool.hatch.build]
sources = ["src"]
packages = ["src/computer_control_mcp"]

